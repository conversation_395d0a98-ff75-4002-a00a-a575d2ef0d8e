# Multiplayer Game Lobby

A comprehensive multiplayer game lobby website built with Flask and Flask-SocketIO, designed for hosting and playing various Japanese card games.

![Game Lobby Screenshot](https://via.placeholder.com/800x450.png?text=Game+Lobby+Screenshot)

*Note: The application is running in Debug mode, so code changes will be automatically applied without needing to restart the server.*

## Features

### Core Functionality
- Create and join game lobbies with unique 6-character codes
- Real-time updates using WebSockets for all lobby events
- Responsive design that works on desktop and mobile devices

### User Experience
- Visual indicators for the current player and host
- Styled game code display for easy copying and sharing
- Exit game functionality with confirmation for hosts
- Real-time player list updates when players join or leave

### Host Controls
- Set maximum number of players (2-10)
- Select from multiple Japanese card games
- Transfer host privileges when leaving
- End game for all players
- Start game when ready

### Game Types
The lobby supports multiple Japanese card games:

1. **Kankei** - A strategic card game focusing on creating relationships between cards
2. **Hayabusa** - A fast-paced game where speed and pattern recognition are key
3. **Sutoppu** - A stopping game where timing is crucial
4. **Canasta** - A rummy-type card game played with multiple decks
5. **Shichinarabe** - A sequencing game where players arrange cards in specific patterns

## Requirements

- Python 3.7+
- Flask
- Flask-SocketIO
- Eventlet

## Installation

1. Clone the repository
2. Install dependencies:
   ```
   pip install -r requirements.txt
   ```
3. Run the application:
   ```
   python app.py
   ```
4. Open your browser and navigate to `http://localhost:5000`

## How to Use

### Hosting a Game

1. Enter your name on the homepage
2. Click "Host Game"
3. Share the generated game code with other players
4. Configure lobby settings:
   - Set the maximum number of players (2-10)
   - Select a card game from the dropdown menu
5. Wait for players to join (you'll see them appear in the player list)
6. Click "Start Game" when ready (requires at least 2 players)
7. To exit the game as host, click "Exit Game" and choose:
   - "Leave & Transfer Host" to transfer host privileges to another player
   - "End Game For Everyone" to close the lobby for all players
   - "Cancel" to stay in the game

### Joining a Game

1. Enter your name on the homepage
2. Click "Join Game"
3. Enter the 6-character game code provided by the host
4. Click "Join"
5. You'll see yourself and other players in the player list (you're marked with "(You)")
6. Wait for the host to start the game
7. To leave the game, click "Exit Game"

## Project Structure

- `app.py`: Main Flask application with routes and WebSocket event handlers
- `static/`: Static files
  - `css/style.css`: Styling for the application
  - `js/main.js`: JavaScript for the homepage
  - `js/lobby.js`: JavaScript for the game lobby page
- `templates/`: HTML templates
  - `index.html`: Homepage template
  - `lobby.html`: Game lobby template
- `requirements.txt`: Project dependencies

## Technical Implementation

### Backend (Flask)

#### Data Structure
The application uses an in-memory dictionary to store game lobbies:
```python
game_lobbies = {
    'GAME_CODE': {
        'host': 'host_name',
        'players': ['player1', 'player2', ...],
        'max_players': 6,
        'started': False,
        'game_type': 'Kankei'
    }
}
```

#### Routes
- `/`: Homepage
- `/host`: Create a new game lobby
- `/join`: Join an existing game lobby
- `/lobby/<game_code>`: Game lobby page
- `/update_max_players`: Update maximum players
- `/update_game_type`: Update the selected game type
- `/start_game`: Start the game
- `/exit_game`: Leave or end the game

#### WebSocket Events
- `connect`: Handle client connection
- `disconnect`: Handle client disconnection
- `player_joined`: Notify when a player joins
- `player_left`: Notify when a player leaves
- `max_players_updated`: Notify when max players is updated
- `game_type_updated`: Notify when game type is updated
- `new_host`: Notify when host privileges are transferred
- `game_started`: Notify when the game starts
- `game_ended`: Notify when the game ends

### Frontend

#### Real-time Updates
The application uses Socket.IO to provide real-time updates to all players in a lobby. When an event occurs (e.g., a player joins or the host changes the game type), the server emits a WebSocket event that is received by all connected clients.

#### User Interface
- **Homepage**: Simple form to enter name and choose to host or join a game
- **Lobby Page**: Displays game code, player list, and host controls
- **Host Controls**: Forms to update max players and game type
- **Player Indicators**: Visual indicators for the current player and host
- **Exit Confirmation**: Modal dialog for host to confirm exit action

## Card Game Rules

### Kankei
Kankei is a strategic card game where players create relationships between cards. Players take turns placing cards to form connections based on suit, number, or special combinations. The goal is to create the most valuable connections by the end of the game.

### Hayabusa
Hayabusa is a fast-paced card game where speed and pattern recognition are key. Players race to identify and claim patterns in the displayed cards. The first player to correctly identify a valid pattern claims those cards. The player with the most cards at the end wins.

### Sutoppu
Sutoppu is a stopping game where timing is crucial. Players take turns drawing cards and adding them to a central pile. Players must decide when to "stop" based on the current state of the pile. Stopping at the right moment earns points, but stopping too early or too late results in penalties.

### Canasta
Canasta is a rummy-type card game played with multiple decks. Players form teams and try to create melds of cards. The goal is to score points by making canastas (seven or more cards of the same rank). The game ends when a player goes out by getting rid of all their cards.

### Shichinarabe
Shichinarabe is a sequencing game where players arrange cards in specific patterns. Players take turns placing cards to form sequences of the same suit or sets of the same rank. The goal is to be the first player to arrange all your cards in valid combinations.

## Adding New Game Types

To add a new game type to the system:

1. **Update the valid game types list in `app.py`**:
   ```python
   valid_game_types = ['Kankei', 'Hayabusa', 'Sutoppu', 'Canasta', 'Shichinarabe', 'NewGameType']
   ```

2. **Add the new game option to the dropdown in `lobby.html`**:
   ```html
   <option value="NewGameType">New Game Type</option>
   ```

3. **Add game rules to the README.md file**:
   ```markdown
   ### NewGameType
   Description of the new game type and its rules...
   ```

4. **Implement the actual game logic** (if you're extending beyond just the lobby system)

## Design System

The UI/UX of the application follows a consistent design system with the following components:

### Layout System

The application uses a responsive 12-column grid system with three distinct breakpoints:

- **Mobile**: < 768px
- **Tablet**: 768px - 1024px
- **Desktop**: > 1024px

Consistent spacing is applied throughout the application:
- Margins: 20px
- Padding: 16px
- Element spacing: 16px between related elements, 32px between distinct sections

### Color Palette

The application uses a carefully selected color palette:

| Color Name | Hex Code | Usage |
|------------|----------|-------|
| Primary | #3A506B | Primary buttons, headings, important UI elements |
| Secondary | #5BC0BE | Secondary buttons, accents, highlights |
| Accent | #FCA311 | Call-to-action elements, important notifications |
| Background | #F8F9FA | Page background |
| Text | #1C2541 | Main text color |
| Success | #4CAF50 | Success messages and indicators |
| Warning | #FF9800 | Warning messages and indicators |
| Error | #F44336 | Error messages and indicators |

Game-specific colors:
- Kankei: #9C89B8 (Purple)
- Hayabusa: #F0A202 (Gold)
- Sutoppu: #0FA3B1 (Turquoise)
- Canasta: #E76F51 (Coral)
- Shichinarabe: #7B9E89 (Sage)

### Typography

The application uses the Inter font family with a consistent type scale:

- Base font size: 14px
- Scale ratio: 1.25
- Heading sizes:
  - h1: 28px
  - h2: 22px
  - h3: 18px
- Font weights:
  - Normal text: 400
  - Medium emphasis: 500
  - Semi-bold: 600
  - Bold (headings): 700

### Component System

The UI is built using a component-based approach with reusable elements:

- **Cards**: Container elements with subtle shadows for visual separation
- **Buttons**: Primary, secondary, and outline variants with consistent styling
- **Forms**: Standardized input fields, labels, and controls
- **Badges**: Visual indicators for game types and player roles
- **Modal dialogs**: Consistent styling for overlays and confirmations

### Responsive Design

The layout adapts to different screen sizes:

- **Desktop**: Multi-column layout with side-by-side panels
- **Tablet**: Reduced margins and adjusted column widths
- **Mobile**: Single column layout with stacked elements and full-width buttons

![Mobile View](https://via.placeholder.com/375x667.png?text=Mobile+View)
![Tablet View](https://via.placeholder.com/768x1024.png?text=Tablet+View)
![Desktop View](https://via.placeholder.com/1200x800.png?text=Desktop+View)

## License

MIT
