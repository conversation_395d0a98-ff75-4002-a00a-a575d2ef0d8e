/*
 * Enhanced UI/UX for Multiplayer Game Lobby
 * A component-based CSS approach using BEM methodology
 */

/* ==================== */
/* CSS VARIABLES        */
/* ==================== */
:root {
    /* Color System */
    --color-primary: #3A506B;        /* Deep blue */
    --color-secondary: #5BC0BE;      /* Teal */
    --color-accent: #FCA311;         /* Amber */
    --color-background: #F8F9FA;     /* Off-white */
    --color-text: #1C2541;           /* Dark blue */
    --color-success: #4CAF50;        /* Green */
    --color-warning: #FF9800;        /* Orange */
    --color-error: #F44336;          /* Red */

    /* Game Type Colors */
    --color-kankei: #9C89B8;         /* Purple */
    --color-hayabusa: #F0A202;       /* Gold */
    --color-sutoppu: #0FA3B1;        /* Turquoise */
    --color-canasta: #E76F51;        /* Coral */
    --color-shichinarabe: #7B9E89;   /* Sage */

    /* Opacity Variants */
    --color-primary-15: rgba(58, 80, 107, 0.15);
    --color-secondary-15: rgba(91, 192, 190, 0.15);

    /* Neutral Colors */
    --color-white: #FFFFFF;
    --color-gray-100: #F8F9FA;
    --color-gray-200: #E9ECEF;
    --color-gray-300: #DEE2E6;
    --color-gray-400: #CED4DA;
    --color-gray-500: #ADB5BD;
    --color-gray-600: #6C757D;
    --color-gray-700: #495057;
    --color-gray-800: #343A40;
    --color-gray-900: #212529;

    /* Typography */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    --font-size-base: 14px;
    --font-size-sm: 12px;
    --font-size-md: 16px;
    --font-size-lg: 18px;
    --font-size-xl: 22px;
    --font-size-xxl: 28px;

    /* Font Weights */
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;

    /* Spacing */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    --spacing-xxl: 48px;

    /* Borders */
    --border-radius-sm: 4px;
    --border-radius-md: 6px;
    --border-radius-lg: 8px;
    --border-width: 1px;

    /* Shadows */
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 2px 4px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 2px 8px rgba(0, 0, 0, 0.1);

    /* Transitions */
    --transition-fast: 0.2s ease-in-out;
    --transition-normal: 0.3s ease-in-out;

    /* Grid */
    --grid-columns: 12;
    --container-margin: 20px;
    --container-padding: 16px;
    --grid-gutter: 16px;

    /* Breakpoints */
    --breakpoint-mobile: 768px;
    --breakpoint-tablet: 1024px;
}

/* ==================== */
/* RESET & BASE STYLES  */
/* ==================== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: var(--font-size-base);
}

body {
    font-family: var(--font-family);
    line-height: 1.6;
    color: var(--color-text);
    background-color: var(--color-background);
}

h1, h2, h3, h4, h5, h6 {
    margin-bottom: var(--spacing-md);
    font-weight: var(--font-weight-bold);
    line-height: 1.2;
}

h1 {
    font-size: var(--font-size-xxl);
    text-align: center;
}

h2 {
    font-size: var(--font-size-xl);
    color: var(--color-primary);
}

h3 {
    font-size: var(--font-size-lg);
}

p {
    margin-bottom: var(--spacing-md);
}

a {
    color: var(--color-primary);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--color-secondary);
}

/* ==================== */
/* GRID SYSTEM          */
/* ==================== */
.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--container-padding);
}

.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 calc(-1 * var(--grid-gutter) / 2);
}

.col {
    flex: 1 0 0%;
    padding: 0 calc(var(--grid-gutter) / 2);
}

/* Generate column classes */
.col-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
.col-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
.col-3 { flex: 0 0 25%; max-width: 25%; }
.col-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
.col-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
.col-6 { flex: 0 0 50%; max-width: 50%; }
.col-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
.col-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
.col-9 { flex: 0 0 75%; max-width: 75%; }
.col-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
.col-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
.col-12 { flex: 0 0 100%; max-width: 100%; }

/* Responsive columns for tablet */
@media (min-width: 768px) and (max-width: 1024px) {
    .col-md-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
    .col-md-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
    .col-md-3 { flex: 0 0 25%; max-width: 25%; }
    .col-md-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
    .col-md-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
    .col-md-6 { flex: 0 0 50%; max-width: 50%; }
    .col-md-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
    .col-md-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
    .col-md-9 { flex: 0 0 75%; max-width: 75%; }
    .col-md-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
    .col-md-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
    .col-md-12 { flex: 0 0 100%; max-width: 100%; }
}

/* Responsive columns for mobile */
@media (max-width: 767px) {
    .col-sm-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
    .col-sm-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
    .col-sm-3 { flex: 0 0 25%; max-width: 25%; }
    .col-sm-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
    .col-sm-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
    .col-sm-6 { flex: 0 0 50%; max-width: 50%; }
    .col-sm-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
    .col-sm-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
    .col-sm-9 { flex: 0 0 75%; max-width: 75%; }
    .col-sm-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
    .col-sm-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
    .col-sm-12 { flex: 0 0 100%; max-width: 100%; }
}

/* ==================== */
/* CARD COMPONENT       */
/* ==================== */
.card {
    background-color: var(--color-white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    margin-bottom: var(--spacing-xl);
    overflow: hidden;
}

.card__header {
    padding: var(--spacing-md) var(--spacing-lg);
    border-bottom: var(--border-width) solid var(--color-gray-200);
}

.card__body {
    padding: var(--spacing-lg);
}

.card__footer {
    padding: var(--spacing-md) var(--spacing-lg);
    border-top: var(--border-width) solid var(--color-gray-200);
}

/* ==================== */
/* FORM COMPONENTS      */
/* ==================== */
.form-group {
    margin-bottom: var(--spacing-md);
}

.form-label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-weight: var(--font-weight-semibold);
    color: var(--color-text);
}

.form-control {
    display: block;
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-base);
    line-height: 1.5;
    color: var(--color-text);
    background-color: var(--color-white);
    border: var(--border-width) solid var(--color-gray-400);
    border-radius: var(--border-radius-sm);
    transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
}

.form-control:focus {
    border-color: var(--color-primary);
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(58, 80, 107, 0.25);
}

.form-control--number {
    width: 80px;
}

.form-select {
    display: block;
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-base);
    line-height: 1.5;
    color: var(--color-text);
    background-color: var(--color-white);
    border: var(--border-width) solid var(--color-gray-400);
    border-radius: var(--border-radius-sm);
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16'%3E%3Cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 16px 12px;
}

.form-select:focus {
    border-color: var(--color-primary);
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(58, 80, 107, 0.25);
}

/* ==================== */
/* BUTTON COMPONENTS    */
/* ==================== */
.btn {
    display: inline-block;
    font-weight: var(--font-weight-semibold);
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    user-select: none;
    border: var(--border-width) solid transparent;
    padding: var(--spacing-sm) var(--spacing-lg);
    font-size: var(--font-size-base);
    line-height: 1.5;
    border-radius: var(--border-radius-sm);
    transition: color var(--transition-fast),
                background-color var(--transition-fast),
                border-color var(--transition-fast),
                box-shadow var(--transition-fast),
                transform var(--transition-fast);
    cursor: pointer;
}

.btn:hover {
    transform: translateY(-2px);
}

.btn:active {
    transform: translateY(0);
}

.btn--primary {
    color: var(--color-white);
    background-color: var(--color-primary);
    border-color: var(--color-primary);
}

.btn--primary:hover {
    background-color: #2c3e50;
    border-color: #2c3e50;
}

.btn--secondary {
    color: var(--color-white);
    background-color: var(--color-secondary);
    border-color: var(--color-secondary);
}

.btn--secondary:hover {
    background-color: #4ca5a3;
    border-color: #4ca5a3;
}

.btn--accent {
    color: var(--color-text);
    background-color: var(--color-accent);
    border-color: var(--color-accent);
}

.btn--accent:hover {
    background-color: #e69500;
    border-color: #e69500;
}

.btn--outline {
    color: var(--color-primary);
    background-color: transparent;
    border-color: var(--color-primary);
}

.btn--outline:hover {
    color: var(--color-white);
    background-color: var(--color-primary);
}

.btn--sm {
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: var(--font-size-sm);
}

.btn--lg {
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: var(--font-size-md);
}

.btn-group {
    display: flex;
    gap: var(--spacing-md);
}

/* ==================== */
/* GAME LOBBY COMPONENTS */
/* ==================== */
.game-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.game-code {
    display: inline-flex;
    align-items: center;
    margin: var(--spacing-md) 0;
}

.game-code__label {
    font-weight: var(--font-weight-semibold);
    margin-right: var(--spacing-sm);
}

.game-code__value {
    background-color: var(--color-gray-200);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius-sm);
    font-family: monospace;
    font-size: var(--font-size-md);
    border: var(--border-width) solid var(--color-gray-400);
    color: var(--color-text);
}

.game-info {
    margin-bottom: var(--spacing-xl);
}

.game-info__item {
    margin-bottom: var(--spacing-md);
}

.game-info__label {
    font-weight: var(--font-weight-semibold);
    color: var(--color-gray-700);
}

.game-info__value {
    font-weight: var(--font-weight-medium);
}

.game-type-badge {
    display: inline-block;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    color: var(--color-white);
}

.game-type-badge--kankei {
    background-color: var(--color-kankei);
}

.game-type-badge--hayabusa {
    background-color: var(--color-hayabusa);
}

.game-type-badge--sutoppu {
    background-color: var(--color-sutoppu);
}

.game-type-badge--canasta {
    background-color: var(--color-canasta);
}

.game-type-badge--shichinarabe {
    background-color: var(--color-shichinarabe);
}

/* Player list styles */
.players-list {
    list-style: none;
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    margin-bottom: var(--spacing-xl);
}

.player-item {
    padding: var(--spacing-md);
    border-bottom: var(--border-width) solid var(--color-gray-200);
    display: flex;
    align-items: center;
    transition: background-color var(--transition-fast);
}

.player-item:last-child {
    border-bottom: none;
}

.player-item__name {
    flex: 1;
    font-weight: var(--font-weight-medium);
}

.player-item__badge {
    margin-left: var(--spacing-sm);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
}

.player-item__badge--host {
    background-color: var(--color-primary-15);
    color: var(--color-primary);
}

/* Public Rooms List Styles */
.public-rooms-list {
    margin-top: var(--spacing-md);
    margin-bottom: var(--spacing-md);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.public-room-item {
    background-color: var(--color-white);
    border: var(--border-width) solid var(--color-gray-300);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    display: flex;
    flex-direction: column;
    transition: transform var(--transition-fast), box-shadow var(--transition-fast);
}

.public-room-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.public-room-item__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
}

.public-room-item__name {
    font-weight: var(--font-weight-semibold);
    font-size: var(--font-size-md);
    color: var(--color-primary);
}

.public-room-item__game-type {
    margin-left: var(--spacing-sm);
}

.public-room-item__details {
    display: flex;
    justify-content: space-between;
    margin-bottom: var(--spacing-md);
    font-size: var(--font-size-sm);
    color: var(--color-gray-700);
}

.public-room-item .btn {
    align-self: flex-end;
}

/* Checkbox styles */
.checkbox {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-sm);
}

.checkbox input[type="checkbox"] {
    margin-right: var(--spacing-sm);
}

.form-text {
    display: block;
    margin-top: var(--spacing-xs);
    font-size: var(--font-size-sm);
    color: var(--color-gray-600);
}

.player-item__badge--you {
    background-color: var(--color-secondary-15);
    color: var(--color-secondary);
}

/* Host controls */
.host-controls {
    margin-bottom: var(--spacing-xl);
}

.host-controls__form {
    margin-bottom: var(--spacing-md);
}

.host-controls__title {
    font-size: var(--font-size-md);
    font-weight: var(--font-weight-semibold);
    margin-bottom: var(--spacing-sm);
    color: var(--color-primary);
}

/* Game actions */
.game-actions {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-md);
    margin-top: var(--spacing-xl);
}

.waiting-message {
    text-align: center;
    font-style: italic;
    color: var(--color-gray-600);
    margin-bottom: var(--spacing-md);
}

/* Modal styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal__content {
    background-color: var(--color-white);
    margin: 15% auto;
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-lg);
    max-width: 500px;
    box-shadow: var(--shadow-lg);
}

.modal__title {
    margin-top: 0;
    color: var(--color-primary);
    margin-bottom: var(--spacing-md);
}

.modal__text {
    margin-bottom: var(--spacing-lg);
}

.modal__actions {
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-md);
}

/* Utility classes */
.text-center {
    text-align: center;
}

.mb-0 {
    margin-bottom: 0 !important;
}

.mb-1 {
    margin-bottom: var(--spacing-sm) !important;
}

.mb-2 {
    margin-bottom: var(--spacing-md) !important;
}

.mb-3 {
    margin-bottom: var(--spacing-lg) !important;
}

.mb-4 {
    margin-bottom: var(--spacing-xl) !important;
}

.mt-0 {
    margin-top: 0 !important;
}

.mt-1 {
    margin-top: var(--spacing-sm) !important;
}

.mt-2 {
    margin-top: var(--spacing-md) !important;
}

.mt-3 {
    margin-top: var(--spacing-lg) !important;
}

.mt-4 {
    margin-top: var(--spacing-xl) !important;
}

.hidden {
    display: none !important;
}

/* Responsive adjustments */
@media (max-width: 767px) {
    .container {
        padding: var(--spacing-md);
    }

    .card {
        margin-bottom: var(--spacing-lg);
    }

    .card__body {
        padding: var(--spacing-md);
    }

    .btn-group {
        flex-direction: column;
    }

    .game-actions {
        width: 100%;
    }

    .game-actions .btn {
        width: 100%;
    }

    .modal__content {
        width: 90%;
        padding: var(--spacing-lg);
    }

    .modal__actions {
        flex-direction: column;
    }
}
