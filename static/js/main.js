document.addEventListener('DOMContentLoaded', function() {
    // Get DOM elements
    const playerForm = document.getElementById('player-form');
    const playerNameInput = document.getElementById('player-name');
    const hostGameBtn = document.getElementById('host-game-btn');
    const joinGameBtn = document.getElementById('join-game-btn');
    const joinGameForm = document.getElementById('join-game-form');
    const gameCodeInput = document.getElementById('game-code');
    const submitJoinBtn = document.getElementById('submit-join-btn');
    const cancelJoinBtn = document.getElementById('cancel-join-btn');
    const refreshRoomsBtn = document.getElementById('refresh-rooms-btn');
    const publicRoomsContainer = document.getElementById('public-rooms-container');

    // Host game button click handler
    hostGameBtn.addEventListener('click', function() {
        const playerName = playerNameInput.value.trim();
        if (!playerName) {
            alert('Please enter your name');
            return;
        }

        // Create a form and submit it
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '/host';

        const nameInput = document.createElement('input');
        nameInput.type = 'hidden';
        nameInput.name = 'player_name';
        nameInput.value = playerName;

        form.appendChild(nameInput);
        document.body.appendChild(form);
        form.submit();
    });

    // Join game button click handler
    joinGameBtn.addEventListener('click', function() {
        const playerName = playerNameInput.value.trim();
        if (!playerName) {
            alert('Please enter your name');
            return;
        }

        // Show the join game form
        playerForm.style.display = 'none';
        joinGameForm.classList.remove('hidden');
    });

    // Submit join button click handler
    submitJoinBtn.addEventListener('click', function() {
        const playerName = playerNameInput.value.trim();
        const gameCode = gameCodeInput.value.trim().toUpperCase();

        if (!gameCode) {
            alert('Please enter a game code');
            return;
        }

        if (gameCode.length !== 6) {
            alert('Game code must be 6 characters');
            return;
        }

        joinRoom(playerName, gameCode);
    });

    // Cancel join button click handler
    cancelJoinBtn.addEventListener('click', function() {
        joinGameForm.classList.add('hidden');
        playerForm.style.display = 'block';
    });

    // Auto-capitalize game code input
    gameCodeInput.addEventListener('input', function() {
        this.value = this.value.toUpperCase();
    });

    // Function to join a room
    function joinRoom(playerName, gameCode) {
        // Create a form and submit it
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '/join';

        const nameInput = document.createElement('input');
        nameInput.type = 'hidden';
        nameInput.name = 'player_name';
        nameInput.value = playerName;

        const codeInput = document.createElement('input');
        codeInput.type = 'hidden';
        codeInput.name = 'game_code';
        codeInput.value = gameCode;

        form.appendChild(nameInput);
        form.appendChild(codeInput);
        document.body.appendChild(form);
        form.submit();
    }

    // Function to refresh public rooms list
    function refreshPublicRooms() {
        fetch('/get_public_rooms')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updatePublicRoomsList(data.rooms);
                }
            })
            .catch(error => {
                console.error('Error fetching public rooms:', error);
            });
    }

    // Function to update the public rooms list in the UI
    function updatePublicRoomsList(rooms) {
        const publicRoomsList = publicRoomsContainer.querySelector('.public-rooms-list') || document.createElement('div');
        publicRoomsList.className = 'public-rooms-list';
        publicRoomsList.innerHTML = '';

        if (rooms.length === 0) {
            const noRoomsMessage = document.createElement('p');
            noRoomsMessage.className = 'text-muted';
            noRoomsMessage.textContent = 'No public rooms available. Create one by hosting a game!';
            publicRoomsList.appendChild(noRoomsMessage);
        } else {
            rooms.forEach(room => {
                const roomItem = document.createElement('div');
                roomItem.className = 'public-room-item';
                roomItem.dataset.code = room.code;

                const header = document.createElement('div');
                header.className = 'public-room-item__header';

                const name = document.createElement('span');
                name.className = 'public-room-item__name';
                name.textContent = room.display_name;

                const gameType = document.createElement('span');
                gameType.className = `public-room-item__game-type game-type-badge game-type-badge--${room.game_type.toLowerCase()}`;
                gameType.textContent = room.game_type;

                header.appendChild(name);
                header.appendChild(gameType);

                const details = document.createElement('div');
                details.className = 'public-room-item__details';

                const host = document.createElement('span');
                host.className = 'public-room-item__host';
                host.textContent = `Host: ${room.host}`;

                const players = document.createElement('span');
                players.className = 'public-room-item__players';
                players.textContent = `Players: ${room.player_count}/${room.max_players}`;

                details.appendChild(host);
                details.appendChild(players);

                const joinBtn = document.createElement('button');
                joinBtn.className = 'btn btn--primary btn--sm join-public-room-btn';
                joinBtn.textContent = 'Join';
                joinBtn.dataset.code = room.code;

                roomItem.appendChild(header);
                roomItem.appendChild(details);
                roomItem.appendChild(joinBtn);

                publicRoomsList.appendChild(roomItem);
            });
        }

        // Replace the existing list or add the new one
        const existingList = publicRoomsContainer.querySelector('.public-rooms-list');
        if (existingList) {
            publicRoomsContainer.replaceChild(publicRoomsList, existingList);
        } else {
            // Insert before the refresh button
            publicRoomsContainer.insertBefore(publicRoomsList, refreshRoomsBtn);
        }

        // Add event listeners to join buttons
        document.querySelectorAll('.join-public-room-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const playerName = playerNameInput.value.trim();
                if (!playerName) {
                    alert('Please enter your name');
                    return;
                }

                const gameCode = this.dataset.code;
                joinRoom(playerName, gameCode);
            });
        });
    }

    // Refresh rooms button click handler
    if (refreshRoomsBtn) {
        refreshRoomsBtn.addEventListener('click', refreshPublicRooms);
    }

    // Auto-refresh public rooms list every 30 seconds
    setInterval(refreshPublicRooms, 30000);
});
