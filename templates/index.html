<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Game Lobby</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <div class="row">
            <div class="col-12 col-md-8 col-sm-12" style="margin: 0 auto;">
                <div class="card">
                    <div class="card__header">
                        <h1 class="mb-0">Game Lobby</h1>
                    </div>

                    <div class="card__body">
                        {% if error %}
                        <div class="card" style="background-color: #FFEBEE; border-left: 4px solid var(--color-error);">
                            <div class="card__body">
                                <p class="mb-0" style="color: var(--color-error);">{{ error }}</p>
                            </div>
                        </div>
                        {% endif %}

                        <div id="player-form-container">
                            <form id="player-form">
                                <div class="form-group">
                                    <label class="form-label" for="player-name">Your Name:</label>
                                    <input type="text" id="player-name" name="player_name" class="form-control" required>
                                </div>

                                <div class="btn-group mt-3">
                                    <button type="button" id="host-game-btn" class="btn btn--primary">Host Game</button>
                                    <button type="button" id="join-game-btn" class="btn btn--secondary">Join Game</button>
                                </div>
                            </form>

                            <div id="join-game-form" class="hidden mt-3">
                                <div class="form-group">
                                    <label class="form-label" for="game-code">Game Code:</label>
                                    <input type="text" id="game-code" name="game_code" class="form-control" maxlength="6" placeholder="Enter 6-character code" required>
                                </div>

                                <div class="btn-group mt-3">
                                    <button type="button" id="submit-join-btn" class="btn btn--primary">Join</button>
                                    <button type="button" id="cancel-join-btn" class="btn btn--outline">Cancel</button>
                                </div>
                            </div>
                        </div>

                        <!-- Public Rooms Section -->
                        <div id="public-rooms-container" class="mt-4">
                            <h3 class="mb-2">Public Rooms</h3>
                            {% if public_rooms %}
                                <div class="public-rooms-list">
                                    {% for room in public_rooms %}
                                        <div class="public-room-item" data-code="{{ room.code }}">
                                            <div class="public-room-item__header">
                                                <span class="public-room-item__name">{{ room.display_name }}</span>
                                                <span class="public-room-item__game-type game-type-badge game-type-badge--{{ room.game_type|lower }}">{{ room.game_type }}</span>
                                            </div>
                                            <div class="public-room-item__details">
                                                <span class="public-room-item__host">Host: {{ room.host }}</span>
                                                <span class="public-room-item__players">Players: {{ room.player_count }}/{{ room.max_players }}</span>
                                            </div>
                                            <button class="btn btn--primary btn--sm join-public-room-btn" data-code="{{ room.code }}">Join</button>
                                        </div>
                                    {% endfor %}
                                </div>
                            {% else %}
                                <p class="text-muted">No public rooms available. Create one by hosting a game!</p>
                            {% endif %}
                            <button id="refresh-rooms-btn" class="btn btn--outline btn--sm mt-2">Refresh List</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
</body>
</html>
