<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Game Lobby - {{ game_code }}</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
</head>
<body>
    <div class="container">
        <div class="row">
            <div class="col-12 col-md-10 col-sm-12" style="margin: 0 auto;">
                <!-- Header Section -->
                <div class="card mb-3">
                    <div class="card__header">
                        <div class="game-header">
                            <h1 class="mb-2">Game Lobby</h1>
                            <div class="game-code">
                                <span class="game-code__label">Game Code:</span>
                                <span id="game-code" class="game-code__value">{{ game_code }}</span>
                            </div>
                            <p class="mb-0">Share this code with friends to join your game</p>
                        </div>
                    </div>
                </div>

                <!-- Game Information Panel -->
                <div class="row">
                    <div class="col-12 col-md-5 col-sm-12">
                        <div class="card">
                            <div class="card__header">
                                <h2 class="mb-0">Game Information</h2>
                            </div>
                            <div class="card__body">
                                <div class="game-info">
                                    <div class="game-info__item">
                                        <span class="game-info__label">Host:</span>
                                        <span id="host-name" class="game-info__value">{{ host }}</span>
                                    </div>

                                    <div class="game-info__item">
                                        <span class="game-info__label">Game Type:</span>
                                        <span id="game-type-display" class="game-info__value">
                                            <span class="game-type-badge game-type-badge--{{ game_type|lower }}">{{ game_type }}</span>
                                        </span>
                                    </div>

                                    <div class="game-info__item">
                                        <span class="game-info__label">Max Players:</span>
                                        <span id="max-players-display" class="game-info__value">{{ max_players }}</span>
                                    </div>
                                </div>

                                {% if is_host %}
                                <div class="host-controls">
                                    <div class="host-controls__title">Host Controls</div>

                                    <div class="host-controls__form">
                                        <form id="max-players-form">
                                            <div class="form-group">
                                                <label class="form-label" for="max-players">Max Players:</label>
                                                <input type="number" id="max-players" name="max_players" class="form-control form-control--number" min="2" max="10" value="{{ max_players }}">
                                            </div>
                                            <button type="submit" class="btn btn--primary btn--sm">Update</button>
                                        </form>
                                    </div>

                                    <div class="host-controls__form">
                                        <form id="game-type-form">
                                            <div class="form-group">
                                                <label class="form-label" for="game-type">Select Card Game:</label>
                                                <select id="game-type" name="game_type" class="form-select">
                                                    <option value="Kankei" {% if game_type == 'Kankei' %}selected{% endif %}>Kankei</option>
                                                    <option value="Hayabusa" {% if game_type == 'Hayabusa' %}selected{% endif %}>Hayabusa</option>
                                                    <option value="Sutoppu" {% if game_type == 'Sutoppu' %}selected{% endif %}>Sutoppu</option>
                                                    <option value="Canasta" {% if game_type == 'Canasta' %}selected{% endif %}>Canasta</option>
                                                    <option value="Shichinarabe" {% if game_type == 'Shichinarabe' %}selected{% endif %}>Shichinarabe</option>
                                                </select>
                                            </div>
                                            <button type="submit" class="btn btn--primary btn--sm">Update</button>
                                        </form>
                                    </div>

                                    <div class="host-controls__form">
                                        <form id="room-settings-form">
                                            <div class="form-group">
                                                <label class="form-label" for="room-display-name">Room Display Name:</label>
                                                <input type="text" id="room-display-name" name="display_name" class="form-control"
                                                       placeholder="Custom room name (optional)"
                                                       value="{{ display_name or '' }}" maxlength="20">
                                                <small class="form-text text-muted">Leave empty to use game code</small>
                                            </div>

                                            <div class="form-group mt-2">
                                                <div class="checkbox">
                                                    <input type="checkbox" id="room-is-public" name="is_public"
                                                           {% if is_public %}checked{% endif %}>
                                                    <label for="room-is-public">Make this room public</label>
                                                </div>
                                                <small class="form-text text-muted">Public rooms are visible on the homepage</small>
                                            </div>

                                            <button type="submit" class="btn btn--primary btn--sm">Update Room Settings</button>
                                        </form>
                                    </div>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- Player List -->
                    <div class="col-12 col-md-7 col-sm-12">
                        <div class="card">
                            <div class="card__header">
                                <h2 class="mb-0">Players (<span id="player-count">{{ players|length }}</span>/<span id="max-players-count">{{ max_players }}</span>)</h2>
                            </div>
                            <div class="card__body">
                                <ul id="players-list" class="players-list">
                                    {% for player in players %}
                                    <li class="player-item">
                                        <span class="player-item__name">{{ player }}</span>
                                        {% if player == host %}
                                        <span class="player-item__badge player-item__badge--host">Host</span>
                                        {% endif %}
                                        {% if player == session.get('player_name', '') %}
                                        <span class="player-item__badge player-item__badge--you">You</span>
                                        {% endif %}
                                    </li>
                                    {% endfor %}
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons Section -->
                <div class="card">
                    <div class="card__body">
                        {% if is_host %}
                        <div class="game-actions">
                            <button id="start-game-btn" class="btn btn--primary btn--lg">Start Game</button>
                            <button id="exit-game-btn" class="btn btn--secondary">Exit Game</button>
                        </div>
                        {% else %}
                        <div class="game-actions">
                            <div class="waiting-message">
                                <p>Waiting for host to start the game...</p>
                            </div>
                            <button id="exit-game-btn" class="btn btn--secondary">Exit Game</button>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Host Exit Confirmation Modal -->
    <div id="host-exit-modal" class="modal">
        <div class="modal__content">
            <h3 class="modal__title">Exit Game</h3>
            <p class="modal__text">As the host, do you want to:</p>
            <div class="modal__actions">
                <button id="cancel-exit-btn" class="btn btn--outline">Cancel</button>
                <button id="transfer-host-btn" class="btn btn--secondary">Leave & Transfer Host</button>
                <button id="end-game-btn" class="btn btn--primary">End Game For Everyone</button>
            </div>
        </div>
    </div>

    <script>
        // Store session data for JavaScript
        const gameCode = "{{ game_code }}";
        const isHost = {{ 'true' if is_host else 'false' }};
        const playerName = "{{ session.get('player_name', '') }}";

        // Add this to help with session tracking
        document.cookie = "session_test=1; path=/";
    </script>
    <script src="{{ url_for('static', filename='js/lobby.js') }}"></script>
</body>
</html>
